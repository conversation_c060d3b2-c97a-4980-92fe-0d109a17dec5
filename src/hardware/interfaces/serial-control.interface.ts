import { Observable } from 'rxjs';

/**
 * Hardware output mapping for controllino
 */
export interface HardwareMapping {
  output: string;  // Output pin/identifier (e.g., "01", "02", "DOOR", "LIGHT")
  type: 'DIGITAL' | 'ANALOG' | 'COMMAND';  // Type of output
}

/**
 * Simplified serial control interface for basic I/O operations only
 * No animations, displays, or game logic - just hardware control
 */
export interface SerialControlService {
  // Generic command interface
  sendCommand(mapping: HardwareMapping, state: number | string): Promise<void>;

  // Basic I/O convenience methods (these call sendCommand internally)
  turnOnLighting(): Promise<void>;
  turnOffLighting(): Promise<void>;
  openAccessLatch(): Promise<void>;
  closeAccessLatch(): Promise<void>;

  // Basic game control (no timers - managed by middleware)
  startArcades(): Promise<void>;
  stopArcades(): Promise<void>;

  // Event monitoring (only scores come back from controllino)
  onScoresReceived(): Observable<GameScores>;
}

export interface GameScores {
  game1: number;
  game2: number;
  game3: number;
  game4: number;
}

/**
 * Hardware mappings for common controllino outputs
 * These map to actual hardware pins/commands
 */
export const HARDWARE_MAPPINGS = {
  LIGHTING: { output: 'LIGHT', type: 'COMMAND' as const },
  ACCESS_LATCH: { output: 'LATCH', type: 'COMMAND' as const },
  ARCADES: { output: 'ARCADES', type: 'COMMAND' as const },

  // LED outputs (from config)
  RED_LED: { output: '01', type: 'DIGITAL' as const },
  GREEN_LED: { output: '02', type: 'DIGITAL' as const },
  BLUE_LED: { output: '03', type: 'DIGITAL' as const },
} as const;

/**
 * Basic command types for controllino communication
 * Removed all animation/display commands - these are handled by middleware
 */
export enum SerialCommand {
  LIGHT_ON = 'LIGHT_ON',
  LIGHT_OFF = 'LIGHT_OFF',
  OPEN_LATCH = 'OPEN_LATCH',
  CLOSE_LATCH = 'CLOSE_LATCH',
  START_ARCADES = 'START_ARCADES',
  STOP_ARCADES = 'STOP_ARCADES',
  // Generic output control
  OUTPUT_ON = 'OUTPUT_ON',
  OUTPUT_OFF = 'OUTPUT_OFF',
}

/**
 * Only one event type comes back from controllino
 * Timer management is handled by middleware, not hardware
 */
export enum SerialEvent {
  SCORES_RECEIVED = 'SCORES_RECEIVED',
}

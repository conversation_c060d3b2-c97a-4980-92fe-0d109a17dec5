import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Observable, Subject } from 'rxjs';
import * as net from 'net';
import {
  SerialControlService,
  GameScores,
  SerialCommand,
  SerialEvent,
  HardwareMapping,
  HARDWARE_MAPPINGS,
} from '../interfaces/serial-control.interface';

/**
 * TCP-based serial service for development/simulation
 * Connects to the arcade hardware simulator via TCP socket
 */
@Injectable()
export class TcpSerialService implements SerialControlService {
  private client: net.Socket | null = null;
  private scoresReceived$ = new Subject<GameScores>();
  private reconnectInterval: NodeJS.Timeout | null = null;
  private sessionActive = false;

  constructor(private cfg: ConfigService) {
    this.initializeTcpConnection();
  }

  private initializeTcpConnection() {
    const host = this.cfg.get<string>(
      'global.hardware.simulator.host',
      'localhost',
    );
    const port = this.cfg.get<number>('global.hardware.simulator.port', 9999);

    console.log(`🔌 Connecting to arcade simulator at ${host}:${port}`);
    this.connectToSimulator(host, port);
  }

  private connectToSimulator(host: string, port: number) {
    this.client = new net.Socket();

    this.client.connect(port, host, () => {
      console.log('✅ Connected to arcade hardware simulator');
      if (this.reconnectInterval) {
        clearInterval(this.reconnectInterval);
        this.reconnectInterval = null;
      }
    });

    this.client.on('data', (data: Buffer) => {
      this.handleSimulatorData(data.toString());
    });

    this.client.on('error', (error: Error) => {
      // TCP connection error - will retry
      this.scheduleReconnect(host, port);
    });

    this.client.on('close', () => {
      // Connection to simulator closed - will retry
      this.scheduleReconnect(host, port);
    });
  }

  private scheduleReconnect(host: string, port: number) {
    if (this.reconnectInterval) return;

    console.log('🔄 Scheduling reconnection in 5 seconds...');
    this.reconnectInterval = setInterval(() => {
      console.log('🔄 Attempting to reconnect to simulator...');
      this.connectToSimulator(host, port);
    }, 5000);
  }

  private handleSimulatorData(data: string) {
    const trimmedData = data.trim();
    console.log(`📨 Received from simulator: ${trimmedData}`);

    try {
      // Only handle scores - timer management is done by middleware
      if (trimmedData.startsWith(SerialEvent.SCORES_RECEIVED)) {
        console.log('🎯 Processing scores data...');
        this.handleScoresData(trimmedData);
      } else {
        console.log(`📡 Unhandled simulator data: ${trimmedData}`);
      }
    } catch (error) {
      console.error(
        `❌ Error parsing simulator data: ${
          error instanceof Error ? error.message : String(error)
        }`,
      );
    }
  }

  private handleScoresData(data: string) {
    try {
      // Expected format: "SCORES_RECEIVED:{ player1: 100, player2: 200, player3: 0, player4: 150 }"
      const jsonPart = data.substring(data.indexOf(':') + 1);
      const scores: GameScores = JSON.parse(jsonPart);

      console.log(
        `📊 Player scores received from simulator: ${JSON.stringify(scores)}`,
      );
      this.scoresReceived$.next(scores);
    } catch (error) {
      console.error(
        `❌ Error parsing scores data: ${
          error instanceof Error ? error.message : String(error)
        }`,
      );
    }
  }

  // Generic command interface - main method for hardware control
  async sendCommand(mapping: HardwareMapping, state: number | string): Promise<void> {
    return new Promise((resolve, reject) => {
      if (!this.client || this.client.destroyed) {
        const error = new Error('No connection to simulator');
        console.error(`❌ Failed to send command: ${error.message}`);
        reject(error);
        return;
      }

      let commandString: string;

      if (mapping.type === 'DIGITAL') {
        // Digital output: O<PIN><STATE> (e.g., "O011" for pin 01 ON)
        commandString = `O${mapping.output}${state}`;
      } else if (mapping.type === 'COMMAND') {
        // Command-based: <COMMAND>_<STATE> (e.g., "LIGHT_ON" or "LATCH_OPEN")
        commandString = state ? `${mapping.output}_${state}` : mapping.output;
      } else {
        // Analog or other types
        commandString = `${mapping.output}:${state}`;
      }

      this.client.write(`${commandString}\n`, (error) => {
        if (error) {
          console.error(
            `❌ Failed to send command ${commandString}: ${error.message}`,
          );
          reject(error);
        } else {
          // Debug: console.log(`📤 Sent to simulator: ${commandString}`);
          resolve();
        }
      });
    });
  }

  // Legacy method for backward compatibility
  private async sendLegacyCommand(
    command: SerialCommand,
    parameter?: string | number,
  ): Promise<void> {
    return new Promise((resolve, reject) => {
      if (!this.client || this.client.destroyed) {
        const error = new Error('No connection to simulator');
        console.error(`❌ Failed to send command ${command}: ${error.message}`);
        reject(error);
        return;
      }

      const commandString = parameter ? `${command}:${parameter}` : command;

      this.client.write(`${commandString}\n`, (error) => {
        if (error) {
          console.error(
            `❌ Failed to send command ${commandString}: ${error.message}`,
          );
          reject(error);
        } else {
          // Debug: console.log(`📤 Sent to simulator: ${commandString}`);
          resolve();
        }
      });
    });
  }

  // Room control commands - using generic sendCommand with mappings
  async turnOnLighting(): Promise<void> {
    await this.sendCommand(HARDWARE_MAPPINGS.LIGHTING, 'ON');
  }

  async turnOffLighting(): Promise<void> {
    await this.sendCommand(HARDWARE_MAPPINGS.LIGHTING, 'OFF');
  }

  async openAccessLatch(): Promise<void> {
    await this.sendCommand(HARDWARE_MAPPINGS.ACCESS_LATCH, 'OPEN');
  }

  async closeAccessLatch(): Promise<void> {
    await this.sendCommand(HARDWARE_MAPPINGS.ACCESS_LATCH, 'CLOSE');
  }

  // Game control commands - simplified, no timer management
  async startArcades(): Promise<void> {
    this.sessionActive = true;
    await this.sendCommand(HARDWARE_MAPPINGS.ARCADES, 'START');
  }

  async stopArcades(): Promise<void> {
    this.sessionActive = false;
    await this.sendCommand(HARDWARE_MAPPINGS.ARCADES, 'STOP');
  }

  // Event monitoring - only scores come back from controllino
  onScoresReceived(): Observable<GameScores> {
    return this.scoresReceived$.asObservable();
  }

  // Cleanup method
  destroy() {
    if (this.reconnectInterval) {
      clearInterval(this.reconnectInterval);
      this.reconnectInterval = null;
    }
    if (this.client && !this.client.destroyed) {
      this.client.destroy();
    }
  }
}

import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Observable, Subject } from 'rxjs';
import {
  SerialControlService,
  GameScores,
  SerialCommand,
  SerialEvent,
  HardwareMapping,
  HARDWARE_MAPPINGS,
} from '../interfaces/serial-control.interface';

// Import SerialPort for version 9.x
const SerialPort = require('serialport');

@Injectable()
export class ControllinoSerialService implements SerialControlService {
  private serialPort: any;
  private scoresReceived$ = new Subject<GameScores>();

  constructor(private cfg: ConfigService) {
    this.initializeSerial();
  }

  private initializeSerial() {
    const portPath = this.cfg.get<string>(
      'global.hardware.controllino.serial.defaultPort',
      '/dev/ttyACM0',
    );
    const baudRate = this.cfg.get<number>(
      'global.hardware.controllino.serial.baudRate',
      9600,
    );

    // For serialport v9.x, use the constructor with path and options
    this.serialPort = new SerialPort(portPath, {
      baudRate: baudRate,
    });

    this.serialPort.on('data', (data: Buffer) => {
      this.handleSerialData(data.toString());
    });

    this.serialPort.on('error', (error: Error) => {
      console.error(`❌ Serial port error: ${error.message}`);
    });

    console.log(`Serial port initialized on ${portPath} at ${baudRate} baud`);
  }

  private handleSerialData(data: string) {
    const trimmedData = data.trim();
    // Debug: console.log(`📡 Received serial data: ${trimmedData}`);

    try {
      // Only handle scores - timer management is done by middleware
      if (trimmedData.startsWith(SerialEvent.SCORES_RECEIVED)) {
        this.handleScoresData(trimmedData);
      }
    } catch (error) {
      console.error(
        `❌ Error parsing serial data: ${
          error instanceof Error ? error.message : String(error)
        }`,
      );
    }
  }

  private handleScoresData(data: string) {
    try {
      // Expected format: "SCORES_RECEIVED:{ player1: 100, player2: 200, player3: 0, player4: 150 }"
      const jsonPart = data.substring(data.indexOf(':') + 1);
      const scores: GameScores = JSON.parse(jsonPart);

      console.log(`📊 Player scores received: ${JSON.stringify(scores)}`);
      this.scoresReceived$.next(scores);
    } catch (error) {
      console.error(
        `❌ Error parsing scores data: ${
          error instanceof Error ? error.message : String(error)
        }`,
      );
    }
  }

  // Generic command interface - main method for hardware control
  async sendCommand(mapping: HardwareMapping, state: number | string): Promise<void> {
    return new Promise((resolve, reject) => {
      let commandString: string;

      if (mapping.type === 'DIGITAL') {
        // Digital output: O<PIN><STATE> (e.g., "O011" for pin 01 ON)
        commandString = `O${mapping.output}${state}`;
      } else if (mapping.type === 'COMMAND') {
        // Command-based: <COMMAND>:<STATE> (e.g., "LIGHT_ON" or "LIGHT:ON")
        commandString = state ? `${mapping.output}_${state}` : mapping.output;
      } else {
        // Analog or other types
        commandString = `${mapping.output}:${state}`;
      }

      this.serialPort.write(
        `${commandString}\n`,
        (error: Error | null | undefined) => {
          if (error) {
            console.error(
              `❌ Failed to send command ${commandString}: ${error.message}`,
            );
            reject(error);
          } else {
            // Debug: console.log(`📤 Sent command: ${commandString}`);
            resolve();
          }
        },
      );
    });
  }

  // Legacy method for backward compatibility
  private async sendLegacyCommand(
    command: SerialCommand,
    parameter?: string | number,
  ): Promise<void> {
    return new Promise((resolve, reject) => {
      const commandString = parameter ? `${command}:${parameter}` : command;

      this.serialPort.write(
        `${commandString}\n`,
        (error: Error | null | undefined) => {
          if (error) {
            console.error(
              `❌ Failed to send command ${commandString}: ${error.message}`,
            );
            reject(error);
          } else {
            // Debug: console.log(`📤 Sent command: ${commandString}`);
            resolve();
          }
        },
      );
    });
  }

  // Room control commands - using generic sendCommand with mappings
  async turnOnLighting(): Promise<void> {
    await this.sendCommand(HARDWARE_MAPPINGS.LIGHTING, 'ON');
  }

  async turnOffLighting(): Promise<void> {
    await this.sendCommand(HARDWARE_MAPPINGS.LIGHTING, 'OFF');
  }

  async openAccessLatch(): Promise<void> {
    await this.sendCommand(HARDWARE_MAPPINGS.ACCESS_LATCH, 'OPEN');
  }

  async closeAccessLatch(): Promise<void> {
    await this.sendCommand(HARDWARE_MAPPINGS.ACCESS_LATCH, 'CLOSE');
  }

  // Game control commands - simplified, no timer management
  async startArcades(): Promise<void> {
    await this.sendCommand(HARDWARE_MAPPINGS.ARCADES, 'START');
  }

  async stopArcades(): Promise<void> {
    await this.sendCommand(HARDWARE_MAPPINGS.ARCADES, 'STOP');
  }

  // Event monitoring - only scores come back from controllino
  onScoresReceived(): Observable<GameScores> {
    return this.scoresReceived$.asObservable();
  }
}
